import {
  IconBarrierBlock,
  IconBrowserCheck,
  IconBug,
  IconChecklist,
  IconError404,
  IconHelp,
  IconLayoutDashboard,
  IconLock,
  IconLockAccess,
  IconMessages,
  IconNotification,
  IconPackages,
  IconPalette,
  IconServerOff,
  IconSettings,
  IconTool,
  IconUserCog,
  IconUserOff,
  IconUsers,
  IconBuilding,
  IconPlane,
} from '@tabler/icons-react'
import { AudioWaveform, Command, GalleryVerticalEnd, Clock, Receipt, CalendarOff, PieChart } from 'lucide-react'
import type { SidebarData, NavGroup } from '../types'
import type { User, UserPermissions } from '@/types/user'

/**
 * Generate dynamic sidebar data based on user permissions
 */
export const generateSidebarData = (
  user: User | null,
  permissions: UserPermissions,
  isAuthenticated: boolean
): SidebarData => {
  // Default user data (fallback)
  const userData = user ? {
    name: user.fullName,
    email: user.email,
    avatar: user.avatar || '/avatars/default.jpg',
  } : {
    name: 'Guest User',
    email: '<EMAIL>',
    avatar: '/avatars/default.jpg',
  }

  // Company/team data - could be dynamic based on user's company
  const teams = [
    {
      name: user?.consultant?.firstName ? `${user.consultant.firstName}'s Workspace` : 'ERP System',
      logo: Command,
      plan: user?.isAdmin ? 'Admin Access' : 'Consultant Access',
    },
    {
      name: 'FSLI Group',
      logo: GalleryVerticalEnd,
      plan: 'Enterprise',
    },
    {
      name: 'Consultant Portal',
      logo: AudioWaveform,
      plan: 'Professional',
    },
  ]

  // Generate navigation groups based on permissions
  const navGroups: NavGroup[] = []

  // General section - always visible for authenticated users
  if (isAuthenticated) {
    const generalItems = [
      {
        title: 'Dashboard',
        url: '/' as const,
        icon: IconLayoutDashboard,
      },
      {
        title: 'My Dashboard',
        url: '/mydashboard' as const,
        icon: PieChart,
      },
      {
        title: 'Tasks',
        url: '/tasks' as const,
        icon: IconChecklist,
      },
      {
        title: 'Timesheet',
        url: '/timesheet' as const,
        icon: Clock,
      },
      {
        title: 'Expenses',
        url: '/expenses' as const,
        icon: Receipt,
      },
      {
        title: 'Non-working Days',
        url: '/non-working-days' as const,
        icon: CalendarOff,
      },
    ]

    // Add trips if user has permission
    if (permissions.canViewTrips) {
      generalItems.push({
        title: 'Trips',
        url: '/trips' as const,
        icon: IconPlane,
      })
    }

    // Add apps (always available for authenticated users)
    generalItems.push({
      title: 'Apps',
      url: '/apps' as const,
      icon: IconPackages,
    })

    // Add chats (always available for authenticated users)
    generalItems.push({
      title: 'Chats',
      url: '/chats' as const,
      badge: '3',
      icon: IconMessages,
    })

    navGroups.push({
      title: 'General',
      items: generalItems,
    })
  }

  // Management section - only for users with management permissions
  if (isAuthenticated && (permissions.canViewUsers || permissions.canManageClients || user?.isAdmin)) {
    const managementItems = []

    if (permissions.canViewUsers) {
      managementItems.push({
        title: 'Users',
        url: '/users' as const,
        icon: IconUsers,
      })
    }

    if (permissions.canManageClients) {
      managementItems.push({
        title: 'Clients',
        url: '/clients' as const,
        icon: IconBuilding,
      })
    }

    if (managementItems.length > 0) {
      navGroups.push({
        title: 'Management',
        items: managementItems,
      })
    }
  }

  // Pages section - always visible
  navGroups.push({
    title: 'Pages',
    items: [
      {
        title: 'Auth',
        icon: IconLockAccess,
        items: [
          {
            title: 'Sign In',
            url: '/sign-in',
            icon: IconLock,
          },
          {
            title: 'Sign In (2 Col)',
            url: '/sign-in-2',
            icon: IconLock,
          },
          {
            title: 'Sign Up',
            url: '/sign-up',
            icon: IconUserCog,
          },
          {
            title: 'Forgot Password',
            url: '/forgot-password',
            icon: IconLock,
          },
          {
            title: 'OTP',
            url: '/otp',
            icon: IconLock,
          },
        ],
      },
      {
        title: 'Errors',
        icon: IconBug,
        items: [
          {
            title: 'Unauthorized',
            url: '/401',
            icon: IconLock,
          },
          {
            title: 'Forbidden',
            url: '/403',
            icon: IconUserOff,
          },
          {
            title: 'Not Found',
            url: '/404',
            icon: IconError404,
          },
          {
            title: 'Internal Server Error',
            url: '/500',
            icon: IconServerOff,
          },
          {
            title: 'Maintenance Error',
            url: '/503',
            icon: IconBarrierBlock,
          },
        ],
      },
    ],
  })

  // Other section - always visible
  navGroups.push({
    title: 'Other',
    items: [
      {
        title: 'Settings',
        icon: IconSettings,
        items: [
          {
            title: 'Profile',
            url: '/settings',
            icon: IconUserCog,
          },
          {
            title: 'Account',
            url: '/settings/account',
            icon: IconTool,
          },
          {
            title: 'Appearance',
            url: '/settings/appearance',
            icon: IconPalette,
          },
          {
            title: 'Notifications',
            url: '/settings/notifications',
            icon: IconNotification,
          },
          {
            title: 'Display',
            url: '/settings/display',
            icon: IconBrowserCheck,
          },
        ],
      },
      {
        title: 'Help Center',
        url: '/help-center' as const,
        icon: IconHelp,
      },
    ],
  })

  return {
    user: userData,
    teams,
    navGroups,
  }
}
