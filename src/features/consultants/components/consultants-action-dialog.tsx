'use client'

import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Consultant, ConsultantForm, consultantFormSchema } from '../data/schema'
import { useCreateConsultant, useUpdateConsultant } from '../hooks/use-consultants'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: Consultant
}

export function ConsultantsActionDialog({ currentRow, open, onOpenChange }: Props) {
  const isEdit = !!currentRow
  const createConsultant = useCreateConsultant()
  const updateConsultant = useUpdateConsultant()

  const form = useForm<ConsultantForm>({
    resolver: zodResolver(consultantFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      address: '',
      city: '',
      postalCode: '',
      country: '',
      dateOfBirth: '',
      nationality: '',
      emergencyContactName: '',
      emergencyContactPhone: '',
      bankAccountNumber: '',
      socialSecurityNumber: '',
      taxNumber: '',
      contractStartDate: '',
      contractEndDate: '',
      hourlyRate: undefined,
      isActive: true,
    },
  })

  // Reset form when dialog opens/closes or currentRow changes
  useEffect(() => {
    if (open) {
      if (isEdit && currentRow) {
        form.reset({
          firstName: currentRow.firstName || '',
          lastName: currentRow.lastName || '',
          email: currentRow.email || '',
          phoneNumber: currentRow.phoneNumber || '',
          address: currentRow.address || '',
          city: currentRow.city || '',
          postalCode: currentRow.postalCode || '',
          country: currentRow.country || '',
          dateOfBirth: currentRow.dateOfBirth || '',
          nationality: currentRow.nationality || '',
          emergencyContactName: currentRow.emergencyContactName || '',
          emergencyContactPhone: currentRow.emergencyContactPhone || '',
          bankAccountNumber: currentRow.bankAccountNumber || '',
          socialSecurityNumber: currentRow.socialSecurityNumber || '',
          taxNumber: currentRow.taxNumber || '',
          contractStartDate: currentRow.contractStartDate || '',
          contractEndDate: currentRow.contractEndDate || '',
          hourlyRate: currentRow.hourlyRate || undefined,
          isActive: currentRow.isActive,
        })
      } else {
        form.reset({
          firstName: '',
          lastName: '',
          email: '',
          phoneNumber: '',
          address: '',
          city: '',
          postalCode: '',
          country: '',
          dateOfBirth: '',
          nationality: '',
          emergencyContactName: '',
          emergencyContactPhone: '',
          bankAccountNumber: '',
          socialSecurityNumber: '',
          taxNumber: '',
          contractStartDate: '',
          contractEndDate: '',
          hourlyRate: undefined,
          isActive: true,
        })
      }
    }
  }, [open, isEdit, currentRow, form])

  const onSubmit = async (values: ConsultantForm) => {
    try {
      if (isEdit && currentRow) {
        await updateConsultant.mutateAsync({
          id: currentRow.id,
          data: { ...values, id: currentRow.id },
        })
      } else {
        await createConsultant.mutateAsync(values)
      }
      onOpenChange(false)
    } catch (error) {
      // Error handling is done in the mutation hooks
      console.error('Failed to save consultant:', error)
    }
  }

  const isLoading = createConsultant.isPending || updateConsultant.isPending

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-2xl max-h-[90vh] overflow-hidden flex flex-col'>
        <DialogHeader className='text-left'>
          <DialogTitle>{isEdit ? 'Edit Consultant' : 'Add New Consultant'}</DialogTitle>
          <DialogDescription>
            {isEdit ? 'Update the consultant information here. ' : 'Create a new consultant profile here. '}
            Click save when you&apos;re done.
          </DialogDescription>
        </DialogHeader>
        <div className='flex-1 overflow-y-auto py-1 pr-4 -mr-4'>
          <Form {...form}>
            <form
              id='consultant-form'
              onSubmit={form.handleSubmit(onSubmit)}
              className='space-y-4 p-0.5'
            >
              <div className='grid grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='firstName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name *</FormLabel>
                      <FormControl>
                        <Input placeholder='John' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='lastName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name *</FormLabel>
                      <FormControl>
                        <Input placeholder='Doe' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email *</FormLabel>
                    <FormControl>
                      <Input placeholder='<EMAIL>' type='email' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className='grid grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='phoneNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder='****** 567 8900' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='nationality'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nationality</FormLabel>
                      <FormControl>
                        <Input placeholder='American' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name='address'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Textarea placeholder='123 Main Street' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className='grid grid-cols-3 gap-4'>
                <FormField
                  control={form.control}
                  name='city'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input placeholder='New York' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='postalCode'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Postal Code</FormLabel>
                      <FormControl>
                        <Input placeholder='10001' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='country'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country</FormLabel>
                      <FormControl>
                        <Input placeholder='United States' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='dateOfBirth'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date of Birth</FormLabel>
                      <FormControl>
                        <Input type='date' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='hourlyRate'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hourly Rate (€)</FormLabel>
                      <FormControl>
                        <Input
                          type='number'
                          step='0.01'
                          min='0'
                          placeholder='75.00'
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name='isActive'
                render={({ field }) => (
                  <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                    <div className='space-y-0.5'>
                      <FormLabel className='text-base'>Active Status</FormLabel>
                      <div className='text-sm text-muted-foreground'>
                        Enable or disable this consultant profile
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>
        <DialogFooter>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type='submit'
            form='consultant-form'
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : isEdit ? 'Update Consultant' : 'Create Consultant'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
