'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { Consultant, getConsultantStatus, getConsultantFullName } from '../data/schema'
import { formatCurrency, formatDate } from '@/services/consultants'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow: Consultant
}

export function ConsultantsViewDialog({ currentRow, open, onOpenChange }: Props) {
  const status = getConsultantStatus(currentRow)
  const fullName = getConsultantFullName(currentRow)

  const statusConfig = {
    active: { label: 'Active', variant: 'default' as const },
    inactive: { label: 'Inactive', variant: 'secondary' as const },
    pending: { label: 'Pending', variant: 'outline' as const },
    suspended: { label: 'Suspended', variant: 'destructive' as const },
  }

  const config = statusConfig[status]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-2xl max-h-[90vh] overflow-hidden flex flex-col'>
        <DialogHeader className='text-left'>
          <DialogTitle>Consultant Details</DialogTitle>
          <DialogDescription>
            View detailed information about {fullName}.
          </DialogDescription>
        </DialogHeader>
        
        <div className='flex-1 overflow-y-auto py-1 pr-4 -mr-4'>
          <div className='space-y-6'>
            {/* Basic Information */}
            <div>
              <h3 className='text-lg font-semibold mb-3'>Basic Information</h3>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Full Name</label>
                  <p className='text-sm'>{fullName}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Status</label>
                  <div className='mt-1'>
                    <Badge variant={config.variant}>{config.label}</Badge>
                  </div>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Email</label>
                  <p className='text-sm'>{currentRow.email}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Phone</label>
                  <p className='text-sm'>{currentRow.phoneNumber || '-'}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Nationality</label>
                  <p className='text-sm'>{currentRow.nationality || '-'}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Date of Birth</label>
                  <p className='text-sm'>{currentRow.dateOfBirth ? formatDate(currentRow.dateOfBirth) : '-'}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Address Information */}
            <div>
              <h3 className='text-lg font-semibold mb-3'>Address Information</h3>
              <div className='grid grid-cols-1 gap-4'>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Address</label>
                  <p className='text-sm'>{currentRow.address || '-'}</p>
                </div>
                <div className='grid grid-cols-3 gap-4'>
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>City</label>
                    <p className='text-sm'>{currentRow.city || '-'}</p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>Postal Code</label>
                    <p className='text-sm'>{currentRow.postalCode || '-'}</p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>Country</label>
                    <p className='text-sm'>{currentRow.country || '-'}</p>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Contract Information */}
            <div>
              <h3 className='text-lg font-semibold mb-3'>Contract Information</h3>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Hourly Rate</label>
                  <p className='text-sm'>{currentRow.hourlyRate ? formatCurrency(currentRow.hourlyRate) : '-'}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Contract Start</label>
                  <p className='text-sm'>{currentRow.contractStartDate ? formatDate(currentRow.contractStartDate) : '-'}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Contract End</label>
                  <p className='text-sm'>{currentRow.contractEndDate ? formatDate(currentRow.contractEndDate) : '-'}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Emergency Contact */}
            <div>
              <h3 className='text-lg font-semibold mb-3'>Emergency Contact</h3>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Contact Name</label>
                  <p className='text-sm'>{currentRow.emergencyContactName || '-'}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Contact Phone</label>
                  <p className='text-sm'>{currentRow.emergencyContactPhone || '-'}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Financial Information */}
            <div>
              <h3 className='text-lg font-semibold mb-3'>Financial Information</h3>
              <div className='grid grid-cols-1 gap-4'>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Bank Account Number</label>
                  <p className='text-sm font-mono'>{currentRow.bankAccountNumber || '-'}</p>
                </div>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>Social Security Number</label>
                    <p className='text-sm font-mono'>{currentRow.socialSecurityNumber || '-'}</p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>Tax Number</label>
                    <p className='text-sm font-mono'>{currentRow.taxNumber || '-'}</p>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* System Information */}
            <div>
              <h3 className='text-lg font-semibold mb-3'>System Information</h3>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Created At</label>
                  <p className='text-sm'>{currentRow.createdAt ? formatDate(currentRow.createdAt) : '-'}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Updated At</label>
                  <p className='text-sm'>{currentRow.updatedAt ? formatDate(currentRow.updatedAt) : '-'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
