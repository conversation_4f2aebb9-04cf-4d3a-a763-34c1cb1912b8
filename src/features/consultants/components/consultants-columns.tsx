import { ColumnDef } from '@tanstack/react-table'
import { IconDots } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { DataTableColumnHeader } from './data-table-column-header'
import LongText from '@/components/long-text'
import { useConsultants } from '../context/consultants-context'
import { useUser } from '@/context/user-context'
import { Consultant, getConsultantStatus, getConsultantFullName } from '../data/schema'
import { formatCurrency, formatDate } from '@/services/consultants'

export const columns: ColumnDef<Consultant>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    meta: {
      className: cn(
        'sticky md:table-cell left-0 z-10 rounded-tl',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted'
      ),
    },
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'fullName',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Name' />
    ),
    cell: ({ row }) => {
      const consultant = row.original
      const fullName = getConsultantFullName(consultant)
      return <LongText className='max-w-36'>{fullName}</LongText>
    },
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Email' />
    ),
    cell: ({ row }) => (
      <div className='w-fit text-nowrap'>{row.getValue('email')}</div>
    ),
  },
  {
    accessorKey: 'phoneNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Phone' />
    ),
    cell: ({ row }) => <div>{row.getValue('phoneNumber') || '-'}</div>,
    enableSorting: false,
  },
  {
    id: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    accessorFn: (row) => getConsultantStatus(row),
    cell: ({ row }) => {
      const consultant = row.original
      const status = getConsultantStatus(consultant)

      const statusConfig = {
        active: { label: 'Active', variant: 'default' as const },
        inactive: { label: 'Inactive', variant: 'secondary' as const },
        pending: { label: 'Pending', variant: 'outline' as const },
        suspended: { label: 'Suspended', variant: 'destructive' as const },
      }

      const config = statusConfig[status]

      return (
        <Badge variant={config.variant}>
          {config.label}
        </Badge>
      )
    },
    enableSorting: false,
    filterFn: (row, id, value) => {
      const status = getConsultantStatus(row.original)
      return value.includes(status)
    },
  },
  {
    accessorKey: 'hourlyRate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Hourly Rate' />
    ),
    cell: ({ row }) => {
      const rate = row.getValue('hourlyRate') as number
      return <div>{rate ? formatCurrency(rate) : '-'}</div>
    },
  },
  {
    accessorKey: 'contractStartDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Start Date' />
    ),
    cell: ({ row }) => {
      const date = row.getValue('contractStartDate') as string
      return <div>{date ? formatDate(date) : '-'}</div>
    },
  },
  {
    accessorKey: 'city',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='City' />
    ),
    cell: ({ row }) => <div>{row.getValue('city') || '-'}</div>,
    enableSorting: false,
  },
  {
    id: 'actions',
    meta: {
      className: cn(
        'sticky right-0 z-10 rounded-tr',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted'
      ),
    },
    cell: ({ row }) => {
      const consultant = row.original
      const { setOpen, setCurrentRow } = useConsultants()
      const { permissions } = useUser()

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant='ghost'
              className='flex h-8 w-8 p-0 data-[state=open]:bg-muted'
            >
              <IconDots className='h-4 w-4' />
              <span className='sr-only'>Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='w-[160px]'>
            <DropdownMenuItem
              onClick={() => {
                setCurrentRow(consultant)
                setOpen('view')
              }}
            >
              View Details
            </DropdownMenuItem>
            {permissions.canManageUsers && (
              <>
                <DropdownMenuItem
                  onClick={() => {
                    setCurrentRow(consultant)
                    setOpen('edit')
                  }}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    setCurrentRow(consultant)
                    setOpen('delete')
                  }}
                  className='text-destructive'
                >
                  Delete
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
